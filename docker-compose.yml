version: '3.8'

services:
  postgress:
    image: postgres:15
    container_name: postgres_db
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydb
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  app:
    build: .
    container_name: nextjs_app
    ports:
      - '3000:3000'
    environment:
      - DATABASE_URL: postgresql://myuser:mypassword@[YOUR_SERVER_IP]:5432/mydb
    depends_on:
      - postgres

volumes:
  postgres_data: